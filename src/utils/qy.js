/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { loadAdminAd } from '@/components/_pages/ad-extension/sdk';
import apis from '@/utils/apis';
import { app_uid } from '@/utils/config';
import logger from '@base/utils/logger';
import request from '@base/utils/request';
import {
  getSessionIDFromLoginData,
  getStorage,
  noop,
  reportAnalytics,
  reportAnalyticsUnify,
  setStorage,
} from '@base/utils/utils';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import isFunction from 'lodash/isFunction';
import isPlainObject from 'lodash/isPlainObject';

const kb =
  process.env.PLATFORM_ENV === 'alipay'
    ? my
    : process.env.PLATFORM_ENV === 'swan'
    ? swan
    : process.env.PLATFORM_ENV === 'quick'
    ? qa
    : wx.qy
    ? wx.qy
    : wx;

// 检测是否可用
export function canIUse(resolve, reject) {
  try {
    resolve();
  } catch (err) {
    reject({
      errCode: 1000,
      errMsg: err.message,
    });
  }
}

// 授权并绑定手机号{}
export function authAndBindPhone(data, mobileBound, api, onAuthComplete) {
  const { id } = Taro.userMobileData || {};
  const { toastSuccess = false } = api || {};
  request({
    url:
      process.env.MODE_ENV === 'wkd'
        ? apis[`mobile.${mobileBound ? 'modify' : 'bind'}`]
        : process.env.PLATFORM_ENV !== 'alipay'
        ? `/api/weixin/mini/user/Bind/${!id ? 'bindMobileByMini' : 'upBindMobileByMini'}`
        : '/api/weixin/mini/login/bindAliMiniPhone',
    mastHasMobile: false,
    autoTriggerLoginModal: true,
    quickTriggerThen: !toastSuccess,
    data: {
      id,
      ...data,
      code: process.env.PLATFORM_ENV !== 'alipay' ? Taro.wxAuthCode : null,
    },
    toastError: true,
    toastSuccess,
    onThen: (res, req) => {
      resetByBindMobile(res, req, onAuthComplete);
    },
  });
  Taro.wxAuthCode = null;
}

// 获取微信、支付宝等小程序授权手机号
export function getMiniAppAuthPhone(data, api, onAuthComplete) {
  const { toastSuccess = false } = api || {};
  request({
    url: apis['mobile.getPlatformMobile'],
    mastHasMobile: false,
    autoTriggerLoginModal: true,
    quickTriggerThen: !toastSuccess,
    data: {
      ...data,
      code: process.env.PLATFORM_ENV === 'weapp' ? Taro.wxAuthCode : null,
    },
    toastError: true,
    toastSuccess,
    onThen: (res) => {
      onAuthComplete(res);
    },
  });
  Taro.wxAuthCode = null;
}

// 获取用户手机号
export function getUserMobile(force = false) {
  return new Promise((resolve, reject) => {
    if (Taro.userMobileData && !force) {
      resolve(Taro.userMobileData);
      return;
    }
    request({
      url: apis['mobile.get'],
      toastLoading: false,
      mastHasMobile: false,
      onThen: ({ code, data, msg }) => {
        const { relation, user_name, mobile = user_name, ...rest } = data;
        if (code == 0) {
          Taro.userMobileData = { mobile, ...rest };
          resolve(Taro.userMobileData);
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
}
// 获取用户信息
export function getUserInfo() {
  return new Promise((resolve) => {
    if (process.env.MODE_ENV === 'wkd') {
      resolve();
    } else {
      request({
        url: '/api/weixin/mini/user/Config/userCenter',
        toastLoading: false,
        mastHasMobile: false,
        onThen: ({ data: { nickname = '', mobile = '', avatar_url = '', new_mobile = '' } = {} }) =>
          resolve({ nickname, mobile, avatar_url, new_mobile }),
      });
    }
  });
}
// 更新服务端用户信息
export function updateUserInfo(params) {
  return new Promise((resolve, reject) => {
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/g_wkd/v2/user/putWxUserInfo'
          : '/api/weixin/mini/user/Config/putUserInfo',
      data: { ...params },
      toastLoading: false,
      mastHasMobile: false,
      onThen: ({ code, data, msg }) => {
        if (code === 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
}
/**
 *
 * @description 更新昵称
 * @param {string} nickname
 */
export const updateNickname = (nickname) => {
  return new Promise((resolve, reject) => {
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/v1/user/update'
          : '/api/weixin/mini/user/Config/updateUserNickName',
      toastError: true,
      toastSuccess: '昵称已修改',
      quickTriggerThen: true,
      data: {
        nickname,
      },
      onThen: ({ code }) => {
        if (code == 0) {
          Taro.kbUpdateUserInfo({
            nickname,
          });
          resolve();
        } else {
          reject();
        }
      },
    });
  });
};
// 登录状态
export function login(opts) {
  return new Promise((resolve, reject) => {
    canIUse(() => {
      if (process.env.PLATFORM_ENV === 'alipay') {
        // 兼容支付宝获取用户信息
        kb.getAuthCode({
          scopes: 'auth_base',
          ...opts,
          success: (res) => {
            const { authCode } = res;
            resolve({
              code: authCode,
            });
          },
          fail: reject,
        });
      } else if (process.env.PLATFORM_ENV === 'swan') {
        kb.getLoginCode({
          success: (res) => {
            const { code } = res;
            resolve({
              code,
            });
          },
          fail: reject,
        });
      } else {
        kb.login({
          success: (res) => {
            const { code } = res;
            resolve({
              code,
            });
          },
          fail: reject,
        });
      }
    }, reject);
  });
}

// 设置剪贴板
export function setClipboardData(data, title = '已复制') {
  return Taro.setClipboardData({
    data,
  })
    .then(() => {
      Taro.showToast({
        title,
        icon: 'none',
      });
    })
    .catch((err) => console.log(err));
}
// 保存远程网络图片
export function saveRemoteImageToPhotosAlbum({ filePath }) {
  return new Promise((resolve, reject) => {
    const toastIns = Taro.kbToast({ status: 'loading' });
    Taro.downloadFile({
      url: filePath,
      success: ({ tempFilePath }) => {
        if (tempFilePath) {
          saveImageToPhotosAlbum({ filePath: tempFilePath })
            .then(() => {
              resolve();
            })
            .catch(reject);
        }
        toastIns.close();
      },
      fail: () => {
        toastIns.close();
        reject();
      },
    });
  });
}

// 保存图片到相册
export function saveImageToPhotosAlbum({ filePath }) {
  const triggerError = (err) =>
    Taro.kbToast({
      text: `保存失败：${JSON.stringify(err)}`,
    });
  const triggerSuccess = () => {
    Taro.kbToast({
      text: '图片已保存到相册',
    });
  };
  return process.env.PLATFORM_ENV === 'alipay'
    ? new Promise((resolve, reject) => {
        my.saveImage({
          url: filePath,
          success: (res) => {
            triggerSuccess();
            resolve(res);
          },
          fail: (err) => {
            triggerError(err);
            reject(err);
          },
        });
      })
    : Taro.saveImageToPhotosAlbum({ filePath }).then(triggerSuccess).catch(triggerError);
}

function createPayError(reason) {
  return `支付失败（${reason}）`;
}
// 支付：兼容支付宝与微信
export function requestPayment(data, cancelCallback = noop) {
  return new Promise((resolve, reject) => {
    const { type, appId, ...restData } = data;
    if (data === 'PAY_SUCCESS' || type === 'score') {
      // 支付成功
      resolve();
      return;
    }
    if (process.env.PLATFORM_ENV === 'swan') {
      // 百度支付
      swan.requestPolymerPayment({
        bannedChannels: ['BDWallet'],
        orderInfo: restData,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          const { errMsg = '' } = err || {};
          reject(createPayError(errMsg));
        },
      });
    } else if (process.env.PLATFORM_ENV !== 'alipay') {
      const { sign: paySign, ...rest } = restData;
      kb.requestPayment({
        paySign,
        ...rest,
        success(res) {
          logger.info('支付组件成功', res);
          resolve(res);
        },
        fail(res) {
          console.log('res', res);
          logger.info('支付组件失败', res);
          const { errMsg = '' } = res || {};
          const isCancel = `${errMsg}`.includes('cancel');
          isCancel && cancelCallback();
          reject(createPayError(errMsg));
        },
      });
    } else {
      const { trade_no: tradeNO } = restData;
      kb.tradePay({
        tradeNO,
        success: (res) => {
          const { resultCode } = res;
          if (resultCode == '6001') {
            cancelCallback();
            reject(createPayError('requestPayment:fail cancel'));
          } else {
            resolve(res);
          }
        },
        fail: (res) => {
          reject(createPayError(JSON.stringify(res)));
        },
      });
    }
  });
}

/**
 * @description 插件支付，主要是微信定制小程序依托小邮筒插件进行的支付
 */
export function requestPaymentByPlugin(params = {}) {
  return new Promise((resolve) => {
    const data = {
      ori_sessionid: getSessionIDFromLoginData(Taro.kbLoginData, false),
      ori_app_uid: app_uid,
      ...params,
    };
    const payPlugin = Taro.requirePlugin('payPlugin');
    payPlugin.setPayCallback(resolve);
    payPlugin.setPayData(data);
    Taro.navigateTo({
      url: `plugin://payPlugin/pay`,
    });
  });
}

const addedToMyMiniProgramReport = (data) => {
  const { scene } = data;
  const needReportScene = ['1271', '1103', '1104', '1257'];
  if (needReportScene.includes(`${scene}`)) {
    reportAnalyticsUnify({
      action: 'addedToMyMiniProgram-load',
    });
  }
};

// 后端埋点统计
export function buriedPoint(data) {
  const query = (data && data.query) || {};
  const { source, platform_name, template_name, stat_action } = query;

  addedToMyMiniProgramReport(data);

  // 是否发送统计到友盟
  source &&
    reportAnalytics({
      key: 'launch_source',
      source,
    });

  // 是否发送统计后台
  const send = template_name || platform_name || stat_action;
  const sendRequest = (reqData) => {
    request({
      mastLogin: false,
      toastLoading: false,
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/g_wkd/v2/TemplateStat/templateStat'
          : `/api/weixin/mini/user/TemplateStat/templateStat`,
      data: reqData,
    });
  };
  if (send) {
    login()
      .then(({ code }) => {
        // 获取登录code
        data.code = code;
        sendRequest(data);
      })
      .catch(() => {
        sendRequest(data);
      });
  }
}

export function buriedPointQueryCard(data) {
  if (data && data.waybill_no) {
    request({
      mastLogin: false,
      toastLoading: false,
      url: '/g_wkd/v2/TemplateStat/templateStatic',
      data,
    });
  }
}

// 兼容getAccountInfoSync
export function getAccountInfoSync() {
  try {
    return process.env.PLATFORM_ENV === 'alipay'
      ? {
          miniProgram: kb.getAppIdSync(),
        }
      : {
          miniProgram: {
            appId:
              process.env.MODE_ENV === 'third.post' ? 'wxf9f4629fdcde0cd1' : 'wx1cc70b43e2af1811',
          },
          ...Taro.getAccountInfoSync(),
        };
  } catch (error) {
    return {};
  }
}

// 缓存全局数据
export function createGlobalData() {
  /**
   * 跳转页面将数据绑定到全局
   */
  Taro.globalData = {};
  const { globalData } = Taro;
  //  设置全局数据
  Taro.kbSetGlobalData = (key = 'common', data) => {
    globalData[key] = data;
  };
  // 获取全局数据
  Taro.kbGetGlobalData = (key = 'common') => {
    return globalData[key];
  };
  // 获取全局数据一次随后销毁
  Taro.kbGetGlobalDataOnce = (key) => {
    let result = Taro.kbGetGlobalData(key);
    if (isPlainObject(result)) {
      result = { ...result };
    }
    Taro.kbRemoveGlobalData(key);
    return result;
  };
  // 移除全局数据
  Taro.kbRemoveGlobalData = (key) => {
    delete globalData[key];
  };
}

// 检查更新
export const checkAppUpdate = () => {
  // 可能中断用户操作，先屏蔽
  // const upManager = Taro.getUpdateManager();
  // upManager.onUpdateReady(() => {
  //   Taro.kbModal({
  //     content: "小程序有更新，是否重启小程序？",
  //     cancelText: "暂不重启",
  //     confirmText: "立即重启",
  //     onConfirm: () => {
  //       upManager.applyUpdate();
  //     }
  //   });
  // });
};

//上传图片
export const uploadImage = (params) => {
  return new Promise((resolve) => {
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/v1/WeApp/uploadAttachments'
          : '/api/weixin/mini/realname/parse/uploadAttachments',
      requestDataType: 'file',
      toastError: true,
      data: {
        type: 'upload_package_pic',
        ...params,
      },
      onThen: (res) => {
        resolve(res);
      },
    });
  });
};
const upDakRecordByMobile = (mobile) => {
  if (process.env.MODE_ENV === 'wkd') return Promise.reject();
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/waybill/record/syncDakRecordByMobile',
      data: { mobile },
      mastHasMobile: false,
      onThen(res) {
        const { code } = res;
        if (code == 0) {
          resolve();
        } else {
          reject();
        }
      },
    });
  });
};
/**
 *
 * @description 手机号绑定后重置登录信息
 */
export function resetByBindMobile(res, type, onAuthComplete) {
  // 没有直接返回手机号，需要主动获取
  // 如果绑定失败，则有可能已经绑定手机号了，所以重新获取一下是否有手机号
  if (res.code > 0) return;
  // 绑定个人手机号，更新当前用户信息
  const { data: { phone, mobile = phone, id } = {} } = res || {};
  if (!mobile) return;
  // 更新绑定手机号和更新关联手机号时更新包裹信息
  upDakRecordByMobile(mobile).then(() => {
    Taro.eventCenter.trigger('updatePickUpList');
  });
  if (type === 'family') {
    Taro.navigator({
      post: {
        type: 'updateMobile',
        data: { mobile, id },
      },
    });
    return;
  }
  getUserMobile(true)
    .then(({ mobile }) => {
      if (!mobile) return;
      // 更新用户信息 - 手机号
      Taro.kbUpdateUserInfo({
        mobile,
      });
      if (isFunction(onAuthComplete)) {
        onAuthComplete(mobile);
      } else {
        setTimeout(() => {
          Taro.navigator({
            post: {
              type: 'updateMobile',
            },
          });
        }, 100);
      }
    })
    .catch((err) => {
      const errMsg = err && err.message;
      errMsg &&
        Taro.kbToast({
          text: errMsg,
        });
    });
}

/**
 * tab红点切换
 * @param key moreDot个人中心红点/FLNew福利new标识
 */
export const toggleTabBarRedDot = (status = 'show', index = 3, key = 'moreDot') => {
  if (process.env.MODE_ENV === 'wkd') {
    if (!index < 0) return;
    const trigger = (status = 'show') => {
      if (status === 'show') {
        if (key == 'FLNew') {
          loadAdminAd(5)
            .then((adsList) => {
              const [ad] = adsList || [];
              if (ad && ad.tag) {
                Taro.setTabBarBadge({ index, text: ad.tag });
              }
            })
            .catch(() => {});
        } else {
          Taro.showTabBarRedDot({
            index,
          });
        }
      } else {
        if (key == 'FLNew') {
          Taro.removeTabBarBadge({
            index,
          });
          setStorage({ key, data: true });
        } else {
          Taro.hideTabBarRedDot({
            index,
          });
          setStorage({ key, data: true });
        }
      }
    };
    if (status === 'show') {
      if (key == 'FLNew') {
        // 一天展示一次
        getStorage({
          key,
        })
          .then((res) => {
            const { data, ts } = res.data || {};
            let today = dayjs().format('YYYY-MM-DD');
            let lastTimeDay = dayjs(ts).format('YYYY-MM-DD');
            if (data && today == lastTimeDay) return;
            trigger();
          })
          .catch(() => trigger());
      } else {
        getStorage({
          key,
        })
          .then((res) => {
            const { data } = res.data || {};
            if (data) return;
            trigger();
          })
          .catch(() => trigger());
      }
    } else {
      trigger('hide');
    }
  }
};
