/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '@/styles/animation.scss';
@import '@base/styles/components/toast.scss';
@import '@base/styles/components/modal.scss';
@import '@base/styles/components/action-sheet.scss';
@import '@base/styles/components/float-layout.scss';
@import '@base/styles/components/button.scss';
@import '@base/styles/components/grid.scss';
@import '@base/styles/components/list.scss';
@import '@base/styles/components/long-list.scss';
@import '@base/styles/components/empty.scss';
@import '@base/styles/components/font.scss';
@import '@base/styles/components/tabs.scss';
@import '@base/styles/components/calendar.scss';
@import '@base/styles/components/avatar.scss';
@import '@base/styles/components/article.scss';
@import '@base/styles/components/spacing.scss';
@import '@base/styles/components/input.scss';
@import '@base/styles/components/form.scss';
@import '@base/styles/components/tag.scss';
@import '@base/styles/components/textarea.scss';
@import '@base/styles/components/switch.scss';
@import '@base/styles/components/navigator.scss';
@import '@base/styles/components/pagination.scss';
@import '@base/styles/components/timeline.scss';
@import '@base/styles/components/noticebar.scss';
@import '@base/styles/components/search.scss';
@import '@base/styles/components/audio.scss';
@import '@base/styles/components/speech.scss';
@import '@base/styles/components/loader.scss';
@import '~taro-ui/dist/style/components/activity-indicator.scss';
@import '~taro-ui/dist/style/components/badge.scss';
@import '~taro-ui/dist/style/components/checkbox.scss';
@import '~taro-ui/dist/style/components/curtain.scss';
@import '~taro-ui/dist/style/components/flex.scss';
@import '~taro-ui/dist/style/components/image-picker.scss';
@import '~taro-ui/dist/style/components/icon.scss';
@import '~taro-ui/dist/style/components/loading.scss';
@import '~taro-ui/dist/style/components/progress.scss';
@import '~taro-ui/dist/style/components/swipe-action.scss';
@import '~taro-ui/dist/style/components/search-bar.scss';
@import '~taro-ui/dist/style/components/countdown.scss';
@import '@/components/_pages/patchStyles/index.scss';

// 公共样式
page {
  font-size: $font-size-lg;
  background-color: $color-grey-8;
}

.kb-overlay__transparent {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1089;
  width: 100%;
  height: 100%;
  background-color: rgba($color: #000000, $alpha: 0.1);
}

.at-col {
  white-space: normal;
}

// 订单列表按钮样式调整
.kb-order__bars {
  .at-button {
    &--small {
      &.at-button--circle {
        padding-right: $spacing-h-md;
        padding-left: $spacing-h-md;
      }
    }
  }
}

.kb-order__tabs {
  .at-tabs__item {
    font-size: $font-size-base2;
  }
}

.inline-block {
  display: inline-block;
  vertical-align: middle;
}

.kb-display__inline-block {
  @include inline-block();
}

// 图标
.kb-icon {
  line-height: 1;

  &,
  &__text--ml,
  &__text--mr,
  &__text {
    @include inline-block();
    vertical-align: baseline;
  }

  &__text--ml {
    margin-left: $spacing-h-sm;
  }

  &__text--mr {
    margin-right: $spacing-h-sm;
  }

  &__text--mdl {
    margin-left: $spacing-h-md;
  }

  &__text--mdr {
    margin-right: $spacing-h-md;
  }

  &-spin,
  &-loading {
    animation: loader $animation-duration-slower $ease-in-loader infinite;
  }

  @include kb-icon-size();

  &__around {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    background-color: $color-white;
    border-radius: $border-radius-circle;

    &--image {
      display: inline-block;
      width: 50%;
      height: 50%;
      vertical-align: middle;
    }

    .kb-icon {
      color: $color-white;
      font-size: $font-size-base !important;
      @include kb-icon-size();
    }

    &--large {
      width: 120px;
      height: 120px;
      line-height: 120px;
    }

    &--small {
      width: 80px;
      height: 80px;
      line-height: 80px;
    }

    &--mini {
      width: 50px;
      height: 50px;
      line-height: 50px;
    }

    &--lighter {
      background-color: $color-grey-4;
    }

    &--grey {
      background-color: $color-grey-2;
    }

    &--brand {
      background-color: $color-brand;
    }

    &--brand-lighter {
      background-color: $color-brand-lightest;
    }

    &--pink {
      background-color: $color-pink;
    }
    &--pink1 {
      background-color: $color-pink-1;
    }
    &--red {
      background-color: $color-red;
    }

    &--orange {
      background-color: $color-orange;
    }

    &--blue {
      background-color: $color-blue;
    }
    &--blue-1 {
      background-color: $color-blue-2;
    }
    &--yellow-1 {
      background-color: $color-yellow-1;
    }
    &--green {
      background-color: $color-green;
    }

    &--green-yz {
      background-color: $color-green-yz;
    }
  }

  // 多色
  &__multicolor {
    &-brand {
      @include multicolor($color-brand, $color-brand-light);
    }
    &-blue {
      @include multicolor($color-blue-0, $color-blue-0);
    }
    &-yellow {
      @include multicolor($color-orange-lighter, $color-orange-lighter);
    }
    &-red {
      @include multicolor($color-red-1, $color-red-1);
    }
  }

  //箭头方向 默认向右
  &__direction {
    &-down {
      transform: rotate(90deg);
    }

    &-up {
      transform: rotate(-90deg);
    }

    &-left {
      transform: rotate(180deg);
    }
  }
}

// 滚动区域
.kb-scrollview {
  height: 100%;
}

// 点击样式
.kb-hover-radius,
.kb-hover {
  background-color: $hover-active !important;
  transition: background-color $animation-duration-slow;
}

.kb-hover-radius {
  border-radius: $border-radius-md;
}

.kb-hover-opacity {
  opacity: $opacity-active !important;
  transition: opacity $animation-duration-slow;
}

// 功能盒子
.kb-box {
  box-sizing: border-box;
  margin-bottom: $spacing-v-md;
  overflow: hidden;
  background-color: $color-white;
  border-radius: $border-radius-lg;

  &:last-child {
    margin-bottom: 0;
  }

  &__item {
    &:last-child {
      border-radius: 0 0 $border-radius-lg $border-radius-lg;
    }

    &:first-child {
      border-radius: $border-radius-lg $border-radius-lg 0 0;
    }

    padding: $spacing-v-xl $spacing-h-md;

    &.at-col {
      box-sizing: border-box;
    }
  }
}

// 链接
.kb-link {
  padding: $spacing-v-lg 0;
  color: $color-brand;
}

.kb-clear {
  &__text {
    &-align {
      text-align: left !important;
    }
  }

  &__background {
    &-color {
      background-color: rgba(0, 0, 0, 0) !important;
    }
  }

  &__padding {
    &,
    &-l,
    &-lr,
    & {
      padding-left: 0 !important;
    }
    &,
    &-r,
    &-lr,
    & {
      padding-right: 0 !important;
    }
    &,
    &-b,
    &-tb,
    & {
      padding-bottom: 0 !important;
    }
    &,
    &-t,
    &-tb,
    & {
      padding-top: 0 !important;
    }
  }

  &__margin {
    &-r,
    & {
      margin-right: 0 !important;
    }

    &-b,
    & {
      margin-bottom: 0 !important;
    }

    &-l,
    & {
      margin-left: 0 !important;
    }

    &-r,
    & {
      margin-right: 0 !important;
    }
  }

  &__float {
    overflow: hidden;
  }

  &__pseudo-ele {
    &-after {
      &::after {
        display: none !important;
      }
    }

    &-befor {
      &::before {
        display: none !important;
      }
    }
  }

  &__overflow {
    overflow: visible !important;
  }

  &__border {
    &-radius {
      &,
      &-t {
        &-l,
        & {
          border-top-left-radius: 0 !important;
        }

        &-r,
        & {
          border-top-right-radius: 0 !important;
        }
      }

      &,
      &-b {
        &-l,
        & {
          border-bottom-left-radius: 0 !important;
        }

        &-r,
        & {
          border-bottom-right-radius: 0 !important;
        }
      }
    }
  }
}

// 自定义tab切换
.kb-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-v-xl 0;
  color: #fff;
  background-color: $color-brand;

  &-bar {
    padding: $spacing-v-xs $spacing-h-md;
    border: 2px solid #fff;
    border-left: 0;
    border-radius: 0 $border-radius-md $border-radius-md 0;

    &__current {
      color: $color-brand;
      background-color: #fff;
    }

    &:first-child {
      border-left: 2px solid #fff;
      border-radius: $border-radius-md 0 0 $border-radius-md;
    }
  }
}

.kb-text {
  &__center {
    text-align: center;
  }

  &__left {
    text-align: left;
  }

  &__right {
    text-align: right;
  }

  &__bold {
    font-weight: bold;
  }

  &__v-align-center::before {
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    content: '';
  }
}

.kb-decollator {
  &__bottom {
    border-bottom: $border-lightest;
  }

  &__top {
    border-top: $border-lightest;
  }
}

.kb-edit-ai {
  .at-noticebar {
    color: $color-black-1;
    background-color: rgb(255, 250, 231);
  }
}

.kb-modalOfficialAccount {
  .at-modal__container {
    width: 90%;
  }
  .at-modal__content {
    padding-right: $spacing-h-md;
    padding-left: $spacing-h-md;
  }
}

// 抽奖详情授权手机号样式
.lotteryAuthButton {
  width: 200px;
  height: 200px;
  margin: $spacing-h-lg auto;
  color: #d83e0c;
  font-size: $font-size-lg + 1;
  line-height: 200px;
  text-align: center;
  background-color: #fec804;
  border: none;
  border-radius: $border-radius-arc !important;
}
.lotteryButton {
  color: $color-red;
  background: unset;
  border: 1px solid $color-red;
}

.kb-article {
  padding: $spacing-h-md;
  color: $color-grey-1;
}

// 地址编辑组件
.kb-address-edit-manual {
  .item-content-name {
    .at-input__children::after {
      border-left-width: 0;
    }
  }
  .item-content-company {
    position: relative;
    padding-left: $spacing-h-md;
    &::after {
      position: absolute;
      top: 50%;
      left: 0;
      width: 1px;
      height: 40px;
      background: $color-grey-3;
      transform: translateY(-50%);
      content: '';
    }
  }
}

.kb-maxZIndex {
  top: unset;
  bottom: 0;
  z-index: 9999;
  height: 50%;
  .at-float-layout__overlay {
    display: none;
    background-color: transparent;
  }
}

.kb-background {
  &__white {
    background-color: $color-white;
  }
}

.kb-cabinetOpenModalWrap {
  .at-modal__container {
    border-radius: $border-radius-md;
  }
}
.kb-cabinetOpenModalContent {
  padding: 0 !important;
  &_rejection {
    padding-bottom: 0 !important;
  }
}
.kb-cabinetOpenModalFooter {
  justify-content: space-around !important;
  padding-bottom: 60px !important;
}
.kb-cabinetOpenModal {
  position: relative;
  overflow: hidden;
  text-align: center;
  border-top-left-radius: $border-radius-md;
  border-top-right-radius: $border-radius-md;
  &_warn {
    font-size: 70px;
  }
  // &_info {
  //   padding: $spacing-h-md;
  //   background-color: $color-grey-8;
  //   border-radius: $border-radius-md;
  // }
  .kb-cabinetOpenModal_h350 {
    height: 350px;
  }
  .kb-color-orange {
    color: #ff6645;
  }
  .avatar-img {
    width: 76px;
    height: 76px;
    margin-top: $spacing-h-md;
    border-radius: $border-radius-circle;
  }
  .kb-close {
    position: absolute;
    top: 0px;
    right: 0px;
  }
  .kb-directory {
    width: max-content;
    margin: 0 auto;
    margin-top: $spacing-h-sm;
    padding: $spacing-h-sm $spacing-h-md;
    color: $color-brand;
    font-size: $font-size-lg;
    background-color: #e5f7f3;
    border-radius: 14px;
    &__img {
      width: 60px;
      height: 48px;
      &-revert {
        transform: rotate(180deg);
      }
    }
  }
  .kb-number {
    color: $color-brand;
    font-weight: bold;
    font-size: 72px;
  }
  .kb-text__left {
    text-align: left;
  }
  .kb-background__orange {
    background-color: #fffae7;
  }
  .kb-tips {
    padding: 60px 0;
  }
  .kb-size__48 {
    font-size: 48px;
  }
  .kb-size__72 {
    font-size: 72px;
  }
  .kb-rejection_img {
    width: 150px;
    height: 160px;
    padding-top: $spacing-h-md;
  }
  .kb-w300 {
    width: 300px;
  }
  .kb-rejection-list {
    box-sizing: border-box;
    max-height: 300px;
    margin-bottom: 60px;
    padding: 0 $spacing-h-md;
    background-color: #f8f8f8;
    border-radius: $border-radius-md;
    &-page {
      max-height: 444px;
      padding: 0;
    }
  }
  .kb-margin-40-b {
    margin-bottom: 40px;
  }
  .kb-padding-40-b {
    padding-bottom: 40px;
  }
}

.kb-border {
  border: $border-lightest;
  &-t {
    border-top: $border-lightest;
  }
  &-b {
    border-bottom: $border-lightest;
  }
  &-l {
    border-left: $border-lightest;
  }
  &-r {
    border-right: $border-lightest;
  }
  &-tb {
    border-top: $border-lightest;
    border-bottom: $border-lightest;
  }
  &-lr {
    border-right: $border-lightest;
    border-left: $border-lightest;
  }
}

// 自定义tabBar
.kb-page-custom-bar {
  .kb-page__footer {
    padding-bottom: 100px;
  }

  .w-picker .w-picker-cnt,
  .at-action-sheet__container,
  .at-float-layout__container,
  .kb-scrollview-wrapper .wrapper-footer,
  .yj-container {
    bottom: 100px;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .kb-bind-phone {
    bottom: 100px + $spacing-v-md;
  }
}
.kb-subscribe__initial {
  width: unset;
  height: unset;
  margin: 0;
  padding: 0;
  line-height: unset;
  border: none;
}

.float-pos-rb {
  border: 1px solid red;
  height: 100px;
}