/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbExternalAd from '@/components/_pages/ad-extension/ad';
import {
  MERGE_LIST_KEYS,
  formatRequestQueryList,
  formatResponseQueryList,
  getApiUrlAndDataQueryList,
  mergeQueryPickupListByDak,
} from '@/components/_pages/query/_utils/query.list';
import KbLongList from '@base/components/long-list';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import { useDidShowCom } from '~base/hooks/page';
import { REFRESH_KEY_APPOINTMENT_LIST, refreshControl } from '~/utils/refresh-control';
import './list-content.scss';
import QueryListIndex from './components/queryList';
import CabinetListIndex from './components/cabinetList';
import QueryListStatus from './components/queryList/listStatus';
import isNull from 'lodash/isNull';
import isPlainObject from 'lodash/isPlainObject';
import isUndefined from 'lodash/isUndefined';

const Index = (props) => {
  const {
    show,
    height,
    brands,
    dakId,
    dakAddress,
    type,
    active,
    enableRefresh,
    loadMore,
    onGetted,
    onListReady,
    adUnitIdIndex,
    onReady,
    viewPicture,
    isCabinet,
    dakInfo,
    ...reset
  } = props;

  // console.info('props=======>42', props);

  const service = '取件';
  const { shareKey, mode } = reset;

  // 指定驿站的包裹
  const isAppointment = !!dakId;
  // 投诉
  const isComplaint = type === 'complaint';
  // 他人分享过来的
  const isProxyPickup = !!shareKey;
  // 首页
  const isFirstPage = !isAppointment && !isComplaint;

  const actionRef = useRef({});
  // 空数据图标
  const emptyImage = {
    value: 'no-package',
  };
  // 更新列表
  const [list, updateList] = useState([]);
  // @微快递特有，更新物流信息
  const [flowData, updateFlowData] = useState(null);

  // 合并驿站列表模式下，接口返回空值状态
  const [showMoreDakEmptyStatus, setShowMoreDakEmptyStatus] = useState(false);

  // 传入驿站id则拉取当前驿站的待取列表，否则拉取用户的物流列表
  // 列表请求配置
  const storageKey =
    process.env.MODE_ENV === 'yz' && isFirstPage && type === 'mini_unsign'
      ? `queryList_${type}`
      : '';

  const listData = {
    cachePrimary: false,
    storageKey,
    pageKey: 'pageNum',
    openLocalPageMode: isAppointment || isComplaint,
    api: {
      ...getApiUrlAndDataQueryList({
        isAppointment,
        isComplaint,
        isProxyPickup,
        type,
        dakId,
        isCabinet,
        ...reset,
      }),
      onReady,
      formatRequest: formatRequestQueryList({ actionRef }),
      formatResponse: formatResponseQueryList(
        {
          actionRef,
          isComplaint,
          isAppointment,
          isProxyPickup,
          type,
          isCabinet,
          list,
        },
        (data) => {
          if (data || flowData) {
            updateFlowData({
              ...flowData,
              ...data,
            });
          }
        },
      ),
      moreDataMerger: mergeQueryPickupListByDak,
      onThen: (list, res, req) => {
        const { state } = req;
        const { data } = res;
        const isShowEmptyDakGuide = !data && MERGE_LIST_KEYS.includes(state);
        setShowMoreDakEmptyStatus(isShowEmptyDakGuide);
        updateList(list);
        onGetted(list, res, req);
      },
    },
  };

  // 列表准备就绪
  const handleLongListReady = (ins) => {
    actionRef.current.listIns = ins;
    onListReady(ins);
  };

  // 加载更多
  useEffect(() => {
    // 通过父组件触发翻页
    if (loadMore) {
      actionRef.current.listIns.more();
    }
  }, [loadMore]);

  // 获取品牌列表
  useEffect(() => {
    props.get();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 更新列表
  const handleUpdateList = () => {
    actionRef.current.listIns.loader({ is_cabinet: isCabinet ? 1 : 0 });
  };

  useDidShowCom(() => {
    if (refreshControl(REFRESH_KEY_APPOINTMENT_LIST, 'check')) {
      handleUpdateList();
    }
  });

  const activeMerge = useMemo(() => {
    // 首页，不需要 isCabinet
    if (isUndefined(isCabinet)) return active;

    // 未获取到isCabinet时
    if (isNull(isCabinet) || !active) {
      return false;
    }

    return {
      ...(isPlainObject(active) ? active : {}),
      is_cabinet: isCabinet ? 1 : 0,
    };
  }, [active, isCabinet]);

  return (
    <KbLongList
      active={activeMerge}
      data={listData}
      enableMore
      enableRefresh={enableRefresh}
      scrollY={height !== 'auto'}
      onReady={handleLongListReady}
      height={height}
      topSpaceFix
      noMoreText=''
      noDataText={isAppointment ? '暂无待取包裹' : '暂无包裹'}
      emptyImage={isFirstPage ? false : emptyImage}
      useRenderBottom={showMoreDakEmptyStatus && show}
      renderBottom={
        <View className='kb-spacing-md'>
          <QueryListStatus list={[]} />
        </View>
      }
      renderEmptyAd={
        <View id='float-pos-rb' className='kb-spacing-md-lr'>
          <KbExternalAd adUnitIdIndex={adUnitIdIndex} />
        </View>
      }
    >
      {show && (
        <Fragment>
          {!isCabinet || isProxyPickup ? (
            <QueryListIndex
              list={list}
              flowData={flowData}
              brands={brands}
              viewPicture={viewPicture}
              isFirstPage={isFirstPage}
              isProxyPickup={isProxyPickup}
              isCabinet={isCabinet}
              isAppointment={isAppointment}
              dakId={dakId}
              shareKey={shareKey}
              isComplaint={isComplaint}
              adUnitIdIndex={adUnitIdIndex}
              service={service}
              handleUpdateList={handleUpdateList}
              updateList={updateList}
              showMoreDakEmptyStatus={showMoreDakEmptyStatus}
            />
          ) : (
            <CabinetListIndex
              mode={mode}
              dakInfo={dakInfo}
              data={list}
              updateList={handleUpdateList}
            />
          )}
        </Fragment>
      )}
    </KbLongList>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  dakId: null,
  brands: {},
  type: 'all',
  enableRefresh: true,
  show: true,
  height: 'auto',
  onGetted: noop,
  onReady: noop,
  onListReady: noop,
};

export default connect(
  ({ global: { brands = {} } }) => ({
    brands,
  }),
  {
    get,
  },
)(Index);
