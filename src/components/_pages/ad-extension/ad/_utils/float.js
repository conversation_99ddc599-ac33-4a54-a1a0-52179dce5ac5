import { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption(isOverlapping, coverData) {
  const ref = useRef({ curX: 0, curY: 0, imageSize: { width: 0, height: 0 } }); // 移动位置以及图片尺寸
  const [posX, setPosX] = useState();
  const [posY, setPosy] = useState();

  // 碰撞检测和位置调整的回调
  const [triggerGetBounding] = useBoundingClientRect((floatElementRect) => {
    checkCollisionAndAdjustPosition(floatElementRect);
  });

  // 检测碰撞并调整位置
  const checkCollisionAndAdjustPosition = (floatElementRect) => {
    if (!coverData || !floatElementRect) return;

    const { top: floatTop, height: floatHeight } = floatElementRect;
    const { top: targetTop, height: targetHeight } = coverData;

    console.log('触屏结束后碰撞检测:', { floatTop, floatHeight, targetTop, targetHeight });

    // 检测是否有碰撞
    if (floatTop !== undefined && floatHeight !== undefined &&
        targetTop !== undefined && targetHeight !== undefined) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;
      const hasCollision = floatBottom > targetTop && floatTop < targetBottom;

      console.log('触屏结束碰撞检测结果:', hasCollision);

      if (hasCollision) {
        // 有碰撞，需要调整位置
        const { windowHeight: screenHeight } = getSystemInfoSync();
        let newPosY;

        // 判断浮动元素应该移动到目标元素的上方还是下方
        // 如果目标元素距离屏幕底部的距离 < 150px，则移动到上方
        const distanceFromBottom = screenHeight - targetBottom;

        if (distanceFromBottom < 150) {
          // 移动到目标元素上方
          newPosY = Math.max(0, targetTop - floatHeight - 10); // 10px 间距
          console.log('移动到目标元素上方:', newPosY);
        } else {
          // 移动到目标元素下方
          newPosY = Math.min(screenHeight - floatHeight, targetBottom + 10); // 10px 间距
          console.log('移动到目标元素下方:', newPosY);
        }

        setPosy(newPosY);
      }
    }
  };

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线
    // 判断靠左还是靠右，并吸附
    setPosX(curX);
    setPosy(curY);

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      // 修正吸附逻辑：
      // 如果元素中心点在屏幕左半部分，吸附到左边(x=0)
      // 如果元素中心点在屏幕右半部分，吸附到右边(x=screenWidth-imgWidth)
      const elementCenterX = curX + imgWidth / 2;
      setPosX(elementCenterX < threshold ? 0 : screenWidth - imgWidth);

      // 触屏结束后，检测碰撞并调整位置
      if (coverData) {
        console.log('触屏结束，开始碰撞检测...');
        triggerGetBounding({
          selector: '.kb-query>>>#kb-ad-float__content',
          component: false,
        });
      }
    }, 100);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  useEffect(() => {
    console.log(isOverlapping, '-----useEffect');
  }, [isOverlapping]);

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [coverData, setCoverData] = useState({}); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    setPosReady(true);
    setCoverData(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector, triggerGetBoundingDebounce]);

  // moveArea样式
  const styles = useMemo(() => {
    // const { windowHeight: screenHeight } = getSystemInfoSync();
    // const _top = `${
    //   Math.max(posPatchTop, top + height) > screenHeight - 100
    //     ? top + height
    //     : Math.max(posPatchTop, top + height)
    // }px`;

    return { top: top + height + 'px', bottom: isTabPage ? '100px' : 0 };
  }, [top, height, isTabPage]);

  return {
    coverData,
    posReady,
    styles,
  };
}

export const useCheckIsOverlapping = ({ coverData, active }) => {
  const [isOverlapping, setIsOverlapping] = useState(false);
  const targetElementData = useRef({}); // 重命名为更清晰的变量名

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    calcIsOverlapping(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  const calcIsOverlapping = (floatElementRect) => {
    const { top: floatTop, height: floatHeight } = floatElementRect || {};
    const { top: targetTop, height: targetHeight } = targetElementData.current || {};

    console.log('浮动元素位置:', { floatTop, floatHeight });
    console.log('目标元素位置:', { targetTop, targetHeight });

    // 修正碰撞检测逻辑：
    // 1. 浮动元素底部 > 目标元素顶部 且 浮动元素顶部 < 目标元素底部
    // 2. 确保所有必要的数据都存在
    if (floatTop !== undefined && floatHeight !== undefined &&
        targetTop !== undefined && targetHeight !== undefined) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      const _isOverlapping = floatBottom > targetTop && floatTop < targetBottom;
      console.log('碰撞检测结果:', _isOverlapping, {
        floatTop,
        floatBottom,
        targetTop,
        targetBottom
      });
      setIsOverlapping(_isOverlapping);
    } else {
      console.warn('碰撞检测数据不完整:', { floatTop, floatHeight, targetTop, targetHeight });
      setIsOverlapping(false);
    }
  };

  useEffect(() => {
    if (coverData && active) {
      targetElementData.current = coverData;
      triggerGetBoundingDebounce({
        selector: '.kb-query>>>#kb-ad-float__content',
        component: false,
      });
    }
  }, [active, coverData, triggerGetBoundingDebounce]);

  return isOverlapping;
};
