/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 模板配置项
const templatesMap = {
  orderRefund: {
    label: '返款成功通知',
    ID: 'Larx3d_EiPjvuCz_Vj2uXZ5yV8blG9r898e4lLeT92k',
  },
  orderCancel: {
    label: '订单取消通知',
    ID: 'qmVpFoI4_3GasYQxZAbRwQTyRTasYHe6B4sxjWijDqY',
  },
  orderPay: {
    label: '待付款提醒',
    ID: 'iyJ5cGordAZeDCEcaBxwUk53fniY0PLYXv_9ZGzuMXs',
  },
  orderUpdate: {
    label: '订单状态更新提醒',
    ID: 'Ouu6gB3XJb7RA3pHhFJfHAtNdYqIpeS-D-u8T1n_F1U',
  },
  waybillArrive: {
    label: '快递到站通知',
    ID: '1tKc_ihksW2P_dE-7koZKHzpeESw1xqROGiMcFYYt88',
  },
  waybillUpdate: {
    label: '物流状态提醒',
    ID: 'kDS28SW5sFkWMZv8vBSxs22hS0KSOFcwa_wDOrV3nQc',
  },
  feedback: {
    label: '反馈处理结果通知',
    ID: 'ZOYFIMXDzwuc-Phy2dNvFWwFcaM3DE3GbMBmCMjMtaU',
  },
  delivery: {
    label: '包裹派件通知',
    ID: 'FDN9OP5Iz-O9AbC7uNL_QgLJnaWa88-pXCIkkbVnORg',
  },
  signIn: {
    label: '签收成功通知',
    ID: 'SDqTx3uptaFLbHlEbRc_5Eb7i17fFN-iQmshd2xwfLs',
  },
  revoke: {
    label: '撤单提醒',
    ID: 'E9OCl3vLBc5YgicvS3LmNDXOB79wbCCn8lbNYzFQytI',
  },
  sendBack: {
    label: '退件提醒',
    ID: 'Q0K1asO1b4bfImp6QJH79YplVa3gZlsDlYRu4OvsuAo',
  },
  activity_help1: {
    label: '活动状态提醒',
    ID: 'ppYUVN5bel_phL1jx-JOkl4yIeDv27fYhPQzGn5Tbn8',
  },
  activity_progress: {
    label: '活动进度提醒',
    ID: 'cEd9UrdWHfdhny3G3uy-svDw_J3fmfNMzkclRsQodWA',
  },
  activity_status: {
    label: '活动进度提醒',
    ID: 'Lr03PDnuWMaIkUXof8bxsJsv_RHPcd2ot6y-0FFuh1Q',
  },
};

export default templatesMap;
