/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 订阅模板组合
const idsMap = {
  express:
    process.env.MODE_ENV === 'third'
      ? ['orderUpdate', 'waybillArrive', 'waybillUpdate']
      : ['waybillArrive', 'delivery', 'signIn'], //查询物流订阅
  waitPaid: ['orderUpdate', 'orderPay', 'delivery'], //提交订单订阅
  stoWaitPaid: ['orderUpdate', 'subsidyToAccount', 'delivery'], //申通对象提交订单订阅
  cancel: ['orderCancel', 'orderRefund', 'waybillArrive'], //订单取消订阅
  pay: ['delivery', 'orderRefund', 'orderCancel'], //支付订阅
  feedback: ['feedback'], //投诉订阅
  continuity: ['continuity'], //签到页订阅
  welfare: ['activity_help', 'express_discd'],
  indemnity: ['examine'], // 理赔审核
  refundCard: ['orderRefund'], // 退卡
  withdraw: ['withdraw', 'orderRefund', 'orderUpdate'], //提现
  subscribe: process.env.PLATFORM_ENV === 'weapp' ? ['delivery', 'signIn', 'revoke'] : [],
  appointment:
    process.env.PLATFORM_ENV === 'weapp'
      ? ['waybillArrive', 'signIn', 'sendBack']
      : ['waybillArrive', 'delivery', 'signIn'], //物流页面订阅通知
  activity_special: ['activity_help1', 'activity_progress', 'activity_status'], // 预约取件，取件码取件，寄件tab
};

export default idsMap;
