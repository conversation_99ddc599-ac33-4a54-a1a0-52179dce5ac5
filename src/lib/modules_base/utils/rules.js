/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const rules = {
  phone: {
    //手机号
    code: 3001,
    rule: /^1[2-9]{1}[0-9]{1}\d{8}$/,
    msg: '手机号码输入错误',
    tag: '手机号',
  },
  telPhone: {
    //电话号码
    code: 3002,
    rule: /^(0\d{2,3}(-|\s?))?[2-9]{1}\d{6,7}((-|\s?)\d{1,4})?$/,
    msg: '电话号码输入错误',
    tag: '电话',
  },
  contact: {
    //联系方式
    code: 30012,
    rule: /^1\d{10}([-|\+]{1}\d{3,4})?$|^(0\d{2,3}(-|\s?))?[2-9]{1}\d{6,7}((-|\s?)\d{1,4})?$|^(00)?(886)?[0][9]\d{8}$|^(852)?([6|9])\d{7}|^(853)?[6](8|6)\d{6}$/,
    msg: '请输入正确的手机或固话',
    tag: '联系方式',
  },
  cardId: {
    //身份证号
    code: 3003,
    rule: /^(\d{15}$|^\d{18}$|^\d{17}(\d|X|x))$/,
    msg: '身份证号输入错误',
    tag: '身份证号',
  },
  letter: {
    //字符 字母+数字
    code: 3004,
    rule: /^[A-Za-z0-9]+$/,
    msg: '只允许输入数字和字母',
  },
  number: {
    //数字
    code: 3005,
    type: 'number',
    rule: /^[0-9]\d*$/,
    msg: '只允许输入数字',
  },
  area: {
    //区域规则 上海-上海市-长宁区
    code: 3006,
    rule: /^[\u4e00-\u9fa5]+-[\u4e00-\u9fa5]+-[\u4e00-\u9fa5]+$/,
    msg: '区域格式不合法pp-cc-dd',
    tag: '城市区域',
  },
  m_bracket: {
    //中括号
    code: 3007,
    rule: /\*?\[\]$/,
    msg: '不是[]',
  },
  date: {
    //日期格式
    code: 3008,
    rule: /([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8])))/,
    msg: '日期格式不合法',
    tag: '日期',
  },
  sex: {
    //性别
    code: 3009,
    rule: /^\u7537$|^\u5973$/,
    msg: '只允许输入男或女',
    tag: '性别',
  },
  psw_m: {
    code: 3010,
    rule: /^[A-Za-z0-9:!&%]{6,16}$/,
    msg: '6-16位字符，区分大小写，支持部分特殊字符',
    tag: '密码',
  },
  decimal_1: {
    //支持小数 1位
    code: 3014,
    type: 'number',
    rule: /^(([1-9]{1}\d*)|(0{1}))(\.\d{1})?$/,
    msg: '数字且最多允许一位小数',
  },
  decimal_2: {
    //支持小数 2位
    code: 3011,
    type: 'number',
    rule: /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/,
    msg: '数字且最多允许两位小数',
  },
  hasParam: {
    //带有参数 ?abc=
    code: 3012,
    rule: /\?\w+=/,
    msg: '带有参数,如?abc=',
  },
  url: {
    // 网址
    code: 3013,
    rule: /^(http|https):\/\/[\w\-_\u4E00-\u9FA5:/]+(\.[\w\-_\u4E00-\u9FA5]+)+([\u4E00-\u9FA5!\w\-.,@?^=%&:/~+#]*[\u4E00-\u9FA5!\w\-@?^=%&/~+#])?$/,
    msg: '网址不正确',
    tag: '网址',
  },
  waybill_num: {
    // 运单号
    code: 3014,
    rule: /^([a-zA-Z]*\d+[a-zA-Z]*){8,30}$/,
    msg: '单号不正确',
    tag: '单号',
  },
  address: {
    // 地址
    code: 3015,
    rule: /省|市|县|区|镇|乡|村|路|街|弄|室/,
    msg: '非地址信息',
    tag: '地址',
  },
  bh: {
    //取货码,最多6位
    code: 3016,
    rule: /^[0-9a-zA-Z]{0,2}\d{1,3}$/,
    msg: '取货码最大99999，前两位可为字母',
    tag: '取货码',
  },
  url_mini: {
    // 小程序码
    code: 3017,
    rule: /^\w{1,10}\$(\w{1,10}\:\w{1,20}\+?)+\w{1,20}$/i,
    msg: '不符合约定规则',
    tag: '小程序码',
  },
  phone_star: {
    //手机号
    code: 3018,
    rule: /^1[0-9\*]{10}$/,
    msg: '手机号码输入错误',
    tag: '手机号',
  },
  path: {
    code: 3019,
    rule: /^\/?pages(-[0-3])?\/[a-zA-Z0-9\_\/]+(\/index)?([\u4E00-\u9FA5!\w-./?%&=#]*)?$/,
    msg: '非法路由',
    tag: '',
  },
  email: {
    code: 3020,
    rule: /^[\.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
    msg: 'email格式不符合规则',
    tag: 'email',
  },
  wkdTextLink: {
    code: 4001,
    rule: /^复制这段文字#微快递[0-9a-zA-Z]+#打开微快递微信小程序/,
    msg: '快递码文字链格式不正确',
    tag: '快递码文字链',
  },
  taxNumber: {
    code: 3019,
    rule: /^[(A-H)(J-N)(P-R)(T-U)(W-Y)0-9]{15}$|^[(A-H)(J-N)(P-R)(T-U)(W-Y)0-9]{17,18}$|^[(A-H)(J-N)(P-R)(T-U)(W-Y)0-9]{20}$/,
    msg: '税号格式不正确',
    tag: '公司税号',
  },
  pluginUrl: {
    code: 2021,
    rule: /^plugin:\/\//,
    msg: '插件路径不正确',
    tag: '插件路径',
  },
  tmpFile: {
    code: 3020,
    rule: /^(https?|wxfile|bdfile):\/\//,
    tag: '文件路径',
  },
  specialCharacters: {
    code: 3021,
    rule: /([\u4e00-\u9fa5]|\w|\-|\/|\;|\(|\)|\（|\）|\@|\:|\"|\、|\#|\%|\*|\+|\_|\￥|\$|\~|\^|\.|\——|\……|\||\…|\&|\“|\”|\,|\?|\!|\’|\¥|\。|\，|\[|\]|\{|\}|=|\|<|>|•|\【|\】|\｛|\｝|\《|\》|\‘|\\)+/gi,
    msg: '包含特殊字符',
    tag: '特殊字符',
  },
  shortLink: {
    code: 3022,
    rule: /^\#小程序\:\/\//,
    msg: '小程序短链',
    tag: '短链',
  },
  videoChannel: {
    code: 3022,
    rule: /^video:/,
    msg: '视频号链接不正确',
    tag: '视频号链接',
  },
  thirdAd: {
    code: 3033,
    rule: /^third/,
    msg: '非第三方广告',
    tag: '三方广告',
  },
  httpUrl: {
    code: 3020,
    rule: /^(https?):\/\//,
    tag: '链接',
  },
  liveChannel: {
    code: 3022,
    rule: /^live:/,
    msg: '视频号直播链接不正确',
    tag: '视频号直播链接',
  },
  un_chinese: {
    code: 3023,
    rule: /^[^\u4e00-\u9fa5]+$/,
    msg: '不允许输入中文',
    tag: '非中文',
  },
  insertAdId: {
    code: 3024,
    rule: /^adunit-ab/,
    tag: '插屏广告',
  },
  orderNoticeType: {
    code: 3025,
    rule: /#(yj|gj|dh|wg|xsj|kdy|yz)/,
    tag: '公告类型',
  },
  imgUrl: {
    code: 3025,
    rule: /\.(png|jpg|jpeg|gif|webp)$/,
    tag: '图片',
  },
};

const check = (key, value) => {
  const item = rules[key];
  if (!item) return { code: 0 };
  const { rule, code } = item;
  return {
    ...item,
    code: rule.test(value) ? 0 : code,
  };
};
rules.check = check;
export { rules as default, check };
